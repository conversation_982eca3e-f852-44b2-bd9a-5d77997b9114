import React, { Fragment, useEffect, useRef, useState } from 'react';
import { conveyorControllerWidth, conveyorOperation, conveyorUsage, highResoluBgMegaPixelCount, serverHost } from '../../common/const';
import { Checkbox, ConfigProvider, Select } from 'antd';
import { CustomMenu } from '../../common/styledComponent';
import { useAcquireConveyorControlMutation, useLazyGetAllConveyorStatusQuery, useSubmitConveyorOperationMutation } from '../../services/conveyor';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useTranslation } from 'react-i18next';
import _ from 'lodash';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useInferenceTriggerMutation, useLazyGetAllInspectionsQuery, useLazyGetAllSessionsQuery, useLazyGetInferenceStatusQuery, useStartContinuousInferenceMutation, useStopContinuousInferenceMutation, useStopInferenceMutation } from '../../services/inference';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerLvlLoadingMsg, setConveyorAccessToken, setCurrentControlledConveyorSlotId, setIsContainerLvlLoadingEnabled } from '../../reducer/setting';
import { useGetAllProductsQuery } from '../../services/product';
import { PieChart, Pie, Cell, ResponsiveContainer, Legend } from 'recharts';
import { getCurrentConveyorStatus } from '../../common/util';
import { systemApi } from '../../services/system';
import CommonTable from '../../components/CommonTable';


const Live = () => {
  const [searchParams] = useSearchParams();
  const sessionId = searchParams.get('running-session-id');
  const slotId = searchParams.get('slot-id');
  const goldenProductId = searchParams.get('golden-product-id');

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const curInferenceStatusPolling = useRef();
  const curInfereceDefectCount = useRef(-1);
  const goldenImageContainerRef = useRef(null);
  const curInferenceWatstedCount = useRef(-1);
  const curInferenceTotalCount = useRef(-1);

  const [curSessionInfo, setCurSessionInfo] = useState({});
  const [currentGoldenImageUrl, setCurrentGoldenImageUrl] = useState('');
  const [goldenImageDimension, setGoldenImageDimension] = useState({ width: 0, height: 0 });
  const [isContinuousEnabled, setIsContinuousEnabled] = useState(false);
  const [currentInferenceStatus, setCurrentInferenceStatus] = useState('listening');
  const [isShowDefectiveSubBoard, setIsShowDefectiveSubBoard] = useState(false);
  const [curSubUnitInfo, setCurSubUnitInfo] = useState([]);

  const { data: allProducts } = useGetAllProductsQuery();
  const [submitConveyorOperation] = useSubmitConveyorOperationMutation();
  const [stopSession] = useStopInferenceMutation();
  const [getAllSession] = useLazyGetAllSessionsQuery();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [singleInferenceTrigger] = useInferenceTriggerMutation();
  const [lazyGetAllInspection] = useLazyGetAllInspectionsQuery();
  const [acquireConveyorControl] = useAcquireConveyorControlMutation();
  const [disableContinuousInference] = useStopContinuousInferenceMutation();
  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [enableContinuousInference] = useStartContinuousInferenceMutation();

  const conveyorAccessToken = useSelector(state => state.setting.conveyorAccessToken);
  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const handleContinuousInferenceClick = async (slotId, goldenProductId) => {
    // get current conveyor status
    let status;
    try {
      status = await getCurrentConveyorStatus(
        lazyGetConveyorStatus,
        lazyGetInferenceStatus,
        t
      );
    } catch (error) {
      console.error('Failed to get conveyor status', error);
      aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (_.get(status, `${slotId}.taskType`, '') !== 'inspecting') {
      aoiAlert(t('notification.error.conveyorNotRunningInference'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (!_.get(status, `${slotId}.isContinuous`, false)) {
      // enable
      const res = await enableContinuousInference({
        product_id: Number(goldenProductId), conveyor_id: Number(slotId),
      });

      if (res.error) {
        aoiAlert(t('notification.error.enableContinuousInspection'), ALERT_TYPES.COMMON_ERROR);
        console.error(enableRes.error.message);
        return;
      }

      await initIsContinuousStatus(slotId);

      aoiAlert(t('notification.success.continuousInspectionEnabled'), ALERT_TYPES.COMMON_INFO);
    } else {
      // disable
      const res = await disableContinuousInference({
        conveyor_index: Number(slotId),
      });

      if (res.error) {
        aoiAlert(t('notification.error.stopContinuousInference'), ALERT_TYPES.COMMON_ERROR);
        console.error(disableRes.error.message);
        return;
      }

      await initIsContinuousStatus(slotId);

      aoiAlert(t('notification.success.continuousInspectionStopped'), ALERT_TYPES.COMMON_INFO);
    }
  };

  const handleSubmitConveyorOperation = async (accessToken, operation) => {
    if (_.isEmpty(accessToken)) {
      aoiAlert(t('notification.error.conveyorAccessTokenEmpty'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await submitConveyorOperation({
      conveyor_access_token: accessToken,
      op: operation,
    });

    if (res.error) {
      aoiAlert(t('notification.error.conveyorOperation'), ALERT_TYPES.COMMON_ERROR);
      console.error('submitConveyorOperation error:', _.get(res, 'error.message', ''));
      return;
    }
  };

  const handleSingleInspectionTrigger = async (currentSessionId, slotId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.singleInspectionProcessing')));

    const inferenceStatusRes = await lazyGetInferenceStatus();

    if (inferenceStatusRes.error) {
      aoiAlert(t('notification.error.getInferenceStatus'), ALERT_TYPES.COMMON_ERROR);
      console.error('inferenceStatus error:', _.get(inferenceStatusRes, 'error.message', ''));
      return;
    }

    const res = await singleInferenceTrigger({
      conveyor_index: Number(slotId),
      product_serial: '',
    });

    if (res.error) {
      aoiAlert(t('notification.error.singleInspectionTrigger'), ALERT_TYPES.COMMON_ERROR);
      console.error('singleInferenceTrigger error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    aoiAlert(t('notification.success.singleInspectionTrigger'), ALERT_TYPES.COMMON_INFO);
    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  const handleStopSession = async (slotId) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.stopingInferenceSession')));

    const inferenceStatusRes = await lazyGetInferenceStatus();

    if (inferenceStatusRes.error) {
      aoiAlert(t('notification.error.inferenceStatus'), ALERT_TYPES.COMMON_ERROR);
      console.error('inferenceStatus error:', _.get(inferenceStatusRes, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    const res = await stopSession({
      conveyor_index: Number(slotId),
    });

    if (res.error) {
      aoiAlert(t('notification.error.stopSession'), ALERT_TYPES.COMMON_ERROR);
      console.error('stopSession error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    navigate('/home');
  };

  const initIsContinuousStatus = async (slotId) => {
    let status;

    try {
      status = await getCurrentConveyorStatus(
        lazyGetConveyorStatus,
        lazyGetInferenceStatus,
        t
      );
    } catch (error) {
      console.error('Failed to get conveyor status', error);
      aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    setIsContinuousEnabled(_.get(status, `${slotId}.isContinuous`, false));
  };

  const updateInferenceStatus = async (sessionId, goldenProductId, slotId) => {
    try {
      const inferenceStatusRes = await lazyGetInferenceStatus();

      if (inferenceStatusRes.error) {
        console.error('Failed to get inference status', inferenceStatusRes.error);
        return;
      }


      const conveyorStatus = _.get(inferenceStatusRes, `data.conveyeor_status.${slotId}`, {});
      const statusSessionId = _.get(conveyorStatus, 'session_id', -1);
      const statusGoldenProductId = _.get(conveyorStatus, 'golden_product_id', -1);
      const status = _.get(conveyorStatus, 'status', 'stopped');

      if (Number(statusSessionId) === Number(sessionId) && Number(statusGoldenProductId) === Number(goldenProductId)) {
        setCurrentInferenceStatus(status);
      } else {
        setCurrentInferenceStatus('stopped');
      }
    } catch (error) {
      console.error('Failed to update inference status', error);
    }
  };

  const getStatusDisplayText = (status) => {
    switch (status) {
      case 'listening':
        return t('liveInspection.listening');
      case 'continuous':
        return t('liveInspection.inspecting');
      case 'stopped':
        return t('liveInspection.stopped');
      default:
        return t('liveInspection.unknown');
    }
  };

  useEffect(() => {
    if (_.isEmpty(allProducts) || !goldenImageContainerRef.current) return;

    const goldenProduct = _.find(allProducts, p => Number(p.product_id) === Number(goldenProductId));

    if (!goldenProduct) return;

    setGoldenImageDimension({
      width: goldenImageContainerRef.current.clientWidth,
      height: goldenImageContainerRef.current.clientHeight,
    });

    let goldenImageUrl = `${serverHost}/blob?type=image`;
    goldenImageUrl = goldenImageUrl.concat(`&color_uri=${encodeURIComponent(_.get(goldenProduct, 'inspectables[0].color_map_uri', ''))}`);
    goldenImageUrl = goldenImageUrl.concat(`&depth_uri=${encodeURIComponent(_.get(goldenProduct, 'inspectables[0].depth_map_uri', ''))}`);
    // goldenImageUrl = goldenImageUrl.concat(`&x_min=${_.get(selectedLineItemResult, 'roi.points[0].x', 0)}`);
    // goldenImageUrl = goldenImageUrl.concat(`&y_min=${_.get(selectedLineItemResult, 'roi.points[0].y', 0)}`);
    // goldenImageUrl = goldenImageUrl.concat(`&x_max=${_.get(selectedLineItemResult, 'roi.points[1].x', 0)}`);
    // goldenImageUrl = goldenImageUrl.concat(`&y_max=${_.get(selectedLineItemResult, 'roi.points[1].y', 0)}`);
    // goldenImageUrl = goldenImageUrl.concat(`&angle=${-_.get(selectedLineItemResult, 'roi.angle', 0)}`);
    goldenImageUrl = goldenImageUrl.concat(`&max_megapixel=${highResoluBgMegaPixelCount}`);

    setCurrentGoldenImageUrl(goldenImageUrl);
  }, [
    allProducts,
    goldenProductId,
  ]);

  useEffect(() => {
    curInferenceStatusPolling.current = setInterval(async () => {
      const res = await getAllSession({ complete: -1, ipc_session_id: Number(sessionId) });

      if (res.error) {
        console.error('getAllSession error:', _.get(res, 'error.message', ''));
        aoiAlert(t('notification.error.inferenceStatus'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      setCurSessionInfo(_.get(res, 'data.data[0]', {}));

      // 更新推理状态
      await updateInferenceStatus(sessionId, goldenProductId, slotId);

      if ((curInfereceDefectCount.current == -1 && window.location.href.includes('is-from-review=true')) ||
      (curInfereceDefectCount.current == -1 && _.get(res, 'data.data[0].defective_product_count', 0) === 0)) {
        curInfereceDefectCount.current = _.get(res, 'data.data[0].defective_product_count', 0);
        curInferenceWatstedCount.current = _.get(res, 'data.data[0].wasted_product_count', 0);
        curInferenceTotalCount.current = _.get(res, 'data.data[0].total_product_count', 0);
      } else if (curInfereceDefectCount.current !== _.get(res, 'data.data[0].defective_product_count', 0)) {
        // fetch latest inspection
        const latestIpcRes = await lazyGetAllInspection({ ipc_session_id: Number(sessionId), limit: 1 });

        if (latestIpcRes.error) {
          aoiAlert(t('notification.error.fetchLatestIgetLastestInspectionnspection'), ALERT_TYPES.COMMON_ERROR);
          console.error('latestIpcRes error:', _.get(latestIpcRes, 'error.message', ''));
          return;
        }

        let url = `/inspection/review?ipc-product-id=${_.get(latestIpcRes, 'data.data[0].product_id', 0)}`;
        url += `&ipc-session-id=${_.get(latestIpcRes, 'data.data[0].ipc_session_id', 0)}`;
        url += `&golden-product-id=${_.get(latestIpcRes, 'data.data[0].golden_product_id', 0)}`;
        url += `&is-from-live=true`;
        url += `&slot-id=${slotId}`;

        navigate(url);
      } else if (curInferenceWatstedCount.current !== _.get(res, 'data.data[0].wasted_product_count', 0)) {
        // fetch latest inspection
        const latestIpcRes = await lazyGetAllInspection({ ipc_session_id: Number(sessionId), limit: 1 });

        if (!latestIpcRes.error) {
          setCurSubUnitInfo(_.get(latestIpcRes, 'data.data[0].subunits', []));
          curInferenceWatstedCount.current = _.get(res, 'data.data[0].wasted_product_count', 0);
          setIsShowDefectiveSubBoard(true);
        } else {
          aoiAlert(t('notification.error.fetchLatestIgetLastestInspectionnspection'), ALERT_TYPES.COMMON_ERROR);
          console.error('latestIpcRes error:', _.get(latestIpcRes, 'error.message', ''));
        }
      } else if (curInferenceTotalCount.current !== _.get(res, 'data.data[0].total_product_count', 0)) {
        // new good board
        setIsShowDefectiveSubBoard(false);
      }
      curInferenceTotalCount.current = _.get(res, 'data.data[0].total_product_count', 0);
    }, 500);

    // also acquire conveyor access token
    // NOTE: backend will auto set conveyor usage(the conveyor should already be in used now) we just fetch the token here
    const fetchToken = async (goldenProductId, selectedConveyorSlot) => {
      const acquireConveyorControlRes = await acquireConveyorControl({
        slot_id: Number(selectedConveyorSlot),
        product_id: Number(goldenProductId),
        intension: conveyorUsage.inspection,
      });

      if (acquireConveyorControlRes.error) {
        aoiAlert(t('notification.error.acquireConveyorControl'), ALERT_TYPES.COMMON_ERROR);
        console.error(acquireConveyorControlRes.error.message);
        return;
      }

      dispatch(setConveyorAccessToken(_.get(acquireConveyorControlRes, 'data.conveyor_access_token', '')));
      dispatch(setCurrentControlledConveyorSlotId(Number(selectedConveyorSlot)));
    };

    fetchToken(goldenProductId, slotId);

    initIsContinuousStatus(slotId);

    // 初始化推理状态
    updateInferenceStatus(sessionId, goldenProductId, slotId);

    return () => {
      if (curInferenceStatusPolling.current) clearInterval(curInferenceStatusPolling.current);
      dispatch(setConveyorAccessToken(''));
      dispatch(setCurrentControlledConveyorSlotId(null));
    };
  }, []);


  const totalCount = _.get(curSessionInfo, 'total_product_count', 0);
  const defectiveCount = _.get(curSessionInfo, 'defective_product_count', 0);
  const passCount = totalCount - defectiveCount;

  const data = [
    { name: 'Pass', value: passCount, color: '#27AE60' },
    { name: 'Fail', value: defectiveCount, color: '#E74C3C' }
  ];

  return (
    <div className='flex justify-center items-center flex-1 self-stretch'>
      <div
        className={`flex py-4 px-0.5 flex-col self-stretch border-r-[2px] border-r-[#0000001a] bg-[#ffffff08]`}
        style={{
          width: `${conveyorControllerWidth}px`,
        }}
      >
        <ConfigProvider
          theme={{
            components: {
              Menu: {
                itemHeight: 73,
              }
            }
          }}
        >
          <CustomMenu
            paddingblock={11}
            selectedKeys={[]}
            onSelect={({ e, key }) => {
              switch (key) {
                case 'PCBIn':
                  handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.load);
                  break;
                case 'PCBOut':
                  handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.unload);
                  break;
                case 'passThru':
                  handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.skip);
                  break;
                case 'inspect':
                  handleSingleInspectionTrigger(sessionId, slotId);
                  break;
                case 'stop':
                  handleStopSession(slotId);
                  break;
                case 'continuous':
                  handleContinuousInferenceClick(slotId, goldenProductId);
                  break;
                // case 'reset':
                //   handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.reset);
                //   break;
                default:
                  return;
              }
            }}
            items={[
              {
                key: 'continuous',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch '>
                  {isContinuousEnabled ? (
                    <Fragment>
                      <div className='flex w-8 h-8 justify-center items-center'>
                        <img
                          src='/icn/stop_white.svg'
                          alt='stop'
                          className='w-6 h-6 opacity-50'
                        />
                      </div>
                      <span className='font-source text-[10px] font-normal leading-[150%]'>
                        {t('productDefine.stopContinuous')}
                      </span>
                    </Fragment>
                  ) : (
                    <Fragment>
                      <div className='flex w-8 h-8 justify-center items-center'>
                        <img src='/icn/continuous_white.svg' alt='continuous' className='w-6 h-6 opacity-50' />
                      </div>
                      <span className='font-source text-[10px] font-normal leading-[150%]'>
                        {t('productDefine.continuous')}
                      </span>
                    </Fragment>
                  )}
                </div>,
              },
              {
                key: 'inspect',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch '>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img src='/icn/inspect_white.svg' alt='inspect' className='w-6 h-6 opacity-50' />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.inspect')}
                  </span>
                </div>,
              },
              {
                key: 'stop',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/stop_white.svg'
                      alt='stop'
                      className='w-6 h-6 opacity-50'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.stop')}
                  </span>
                </div>,
                // disabled: true,
              },
              {
                key: 'PCBIn',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/pcbIn_color.svg'
                      alt='stop'
                      className='w-6 h-6'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.PCBIn')}
                  </span>
                </div>,
                disabled: _.isEmpty(conveyorAccessToken),
              },
              {
                key: 'PCBOut',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className={`flex w-8 h-8 justify-center items-center`}>
                    <img
                      src='/icn/pcbOut_color.svg'
                      alt='stop'
                      className={`w-6 h-6`}
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.PCBEject')}
                  </span>
                </div>,
                disabled: _.isEmpty(conveyorAccessToken),
                // disabled: _.isEmpty(conveyorAccessToken) || _.includes(['PCBDimension', 'fullPCBCapture'], recipeActiveTab),
              },
              // {
              //   key: 'clampOn',
              //   label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
              //     <div className='flex w-8 h-8 justify-center items-center'>
              //       <img
              //         src='/icn/clampOn_color.svg'
              //         alt='stop'
              //         className='w-6 h-6'
              //       />
              //     </div>
              //     <span className='font-source text-[10px] font-normal leading-[150%]'>
              //       {t('productDefine.clampOn')}
              //     </span>
              //   </div>
              // },
              // {
              //   key: 'clampOff',
              //   label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
              //     <div className='flex w-8 h-8 justify-center items-center'>
              //       <img
              //         src='/icn/clampOff_color.svg'
              //         alt='stop'
              //         className='w-6 h-6'
              //       />
              //     </div>
              //     <span className='font-source text-[10px] font-normal leading-[150%]'>
              //       {t('productDefine.clampOff')}
              //     </span>
              //   </div>
              // },
              {
                key: 'passThru',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/fastForward_white.svg'
                      alt='stop'
                      className='w-6 h-6'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.passThru')}
                  </span>
                </div>,
                disabled: _.isEmpty(conveyorAccessToken),
                // disabled: _.isEmpty(conveyorAccessToken),
              },
              {
                key: 'reset',
                label: <div className='flex flex-col justify-center items-center gap-1 self-stretch'>
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/reset_white.svg'
                      alt='stop'
                      className='w-6 h-6 opacity-50'
                    />
                  </div>
                  <span className='font-source text-[10px] font-normal leading-[150%]'>
                    {t('productDefine.reset')}
                  </span>
                </div>,
                // disabled: _.isEmpty(conveyorAccessToken),
                disabled: true,
              },
            ]}
          />
        </ConfigProvider>
      </div>
      <div className='flex px-0.5 flex-1 gap-0.5 self-stretch rounded-[6px]'>
        <div className='flex flex-1 flex-col self-stretch'>
          <div
            className='flex h-[40px] py-2 px-4 justify-center items-center self-stretch'
            style={{ background: 'linear-gradient(0deg, rgba(86, 204, 242, 0.10) 0%, rgba(86, 204, 242, 0.10) 100%), #1E1E1E' }}
          >
            <div className='flex items-center gap-6'>
              <div className='flex gap-2 items-center'>

              </div>
              <div className='flex gap-2 items-center'>
                <span className='font-source text-[14px] font-normal leading-[150%]'>
                  {t('productDefine.inspectionCount')}:
                </span>
                <span className='font-source text-[14px] font-semibold leading-[150%]'>
                  {totalCount}
                </span>
              </div>
            </div>
          </div>
          <div className='flex self-stretch flex-1 items-center'>
            {isShowDefectiveSubBoard && !_.isEmpty(curSubUnitInfo) && 
              <div className='flex w-[425px] flex-col items-start self-stretch'>
                <div className='flex flex-col items-start gap-4 self-stretch [background:rgba(244,109,109,0.24)] px-4 py-6'>
                  <div className='flex flex-col items-start gap-4 self-stretch'>
                    <div className='flex flex-col items-start gap-2 self-stretch'>
                      <div className='flex items-center gap-2 self-stretch'>
                        <div className='flex w-[18px] h-[18px] flex-col justify-center items-center'>
                          <img src='/icn/warnCircleFilled_red.svg' alt='warnCircleFilled' className='w-[12px] h-[10.9px]' />
                        </div>
                        <span className='text-white font-source text-base font-semibold leading-[normal]'>
                          {t('liveInspection.defectiveSubBoardFound')}
                        </span>
                      </div>
                      <span className='self-stretch text-white font-source text-sm font-normal leading-[150%]'>
                        {t('liveInspection.numberOfDefectDetected')}
                      </span>
                    </div>
                    <div className='flex items-start gap-8 self-stretch'>
                      <span className='text-white [font-family:"Source_Sans_Pro"] text-sm font-normal leading-[150%]'>
                        {t('liveInspection.subBoardDefectRatioThreshold')}: {_.get(systemMetadata, 'wasted_array_board_failure_ratio', 0.5)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className='flex flex-col items-start flex-[1_0_0] self-stretch p-2'>
                  <div className='flex flex-col items-start gap-px self-stretch p-1 flex-1'>
                    <CommonTable
                      cols={[
                        {
                          key: 'array_index',
                          title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('liveInspection.arrayIndex')}</span>,
                          render: (text, record) => (
                            <span className='font-source text-[12px] font-normal leading-[150%]'>
                              {record.array_index}
                            </span>
                          ),
                        },
                        {
                          key: 'failure_ratio',
                          title: <span className='font-source text-[12px] font-normal leading-[150%] text-gray-5'>{t('liveInspection.failureRatio')}</span>,
                          render: (text, record) => (
                            <span className='font-source text-[12px] font-normal leading-[150%]'>
                              {record.failure_ratio * 100}%
                            </span>
                          ),
                        }
                      ]}
                      data={_.filter(curSubUnitInfo, su => su.wasted)}
                      total={_.filter(curSubUnitInfo, su => su.wasted).length}
                    />
                  </div>
                </div>
              </div>
            }
            <div
              className='flex flex-1 self-stretch'
              ref={goldenImageContainerRef}
            >
              <img
                src={currentGoldenImageUrl}
                className='w-full h-full object-contain'
                alt={'current-golden-image'}
                style={{
                  width: `${goldenImageDimension.width}px`,
                  height: `${goldenImageDimension.height}px`,
                }}
              />
            </div>
          </div>
        </div>
        <div className='flex w-[343px] p-4 flex-col gap-8 self-stretch'>
          <div className='flex p-4 flex-col justify-center items-center self-stretch rounded-[8px] border-[1px] border-[#1CD48C] bg-[#27AE60]'>
            <span className='font-source text-[20px] font-semibold leading-[150%]'>
              {getStatusDisplayText(currentInferenceStatus)}...
            </span>
          </div>
          <div className='flex flex-col gap-2 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('liveInspection.inspecting')}
            </span>
            <span className='font-source text-[18px] font-semibold leading-[150%]'>
              {_.find(allProducts, p => Number(p.product_id) === Number(_.get(curSessionInfo, 'golden_product_id', 0)))?.product_name}
            </span>
          </div>
          <div className='flex py-2 pr-4 pl-2 items-center gap-2 self-stretch rounded-[4px] bg-[#27AE60]'>
            <div className='flex justify-center items-center gap-4 flex-1'>
              <span className='font-source text-[16px] font-normal leading-[150%]'>
                {t('liveInspection.boardFPY')}:
              </span>
              <span className='font-source text-[16px] font-semibold leading-[150%]'>
                {totalCount === 0 ? '-' : `${_.round((passCount/totalCount) * 100, 2)}%`}
              </span>
            </div>
          </div>
          <div className='flex py-2 px-4 items-center gap-2 self-stretch rounded-[4px] bg-[#333]'>
            <div className='flex gap-6 flex-1 self-stretch items-start my-auto'>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('liveInspection.board')}:
              </span>
              <div className='flex flex-col justify-center gap-1 flex-1'>
                <div className='flex items-center gap-2'>
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('liveInspection.inspected')}:
                  </span>
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {totalCount}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('liveInspection.pass')}:
                  </span>
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {passCount}
                  </span>
                </div>
                <div className='flex items-center gap-2'>
                  <span className='font-source text-[12px] font-normal leading-[150%]'>
                    {t('liveInspection.fail')}:
                  </span>
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {defectiveCount}
                  </span>
                </div>
              </div>



            </div>
            <div className='flex w-[131px] h-[75px] self-stretch'>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data}
                    cx="50%"
                    cy="50%"
                    innerRadius={20}
                    outerRadius={35}
                    paddingAngle={0}
                    dataKey="value"
                    startAngle={90}
                    endAngle={450}
                  >
                    {data.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Legend
                    iconType="circle"
                    layout="vertical"
                    verticalAlign="middle"
                    align="right"
                    formatter={(value) => {
                      if (value === 'Pass') {
                        return <span className='font-source text-[8px]'>{t('liveInspection.pass')}</span>;
                      } else if (value === 'Fail') {
                        return <span className='font-source text-[8px]'>{t('liveInspection.fail')}</span>;
                      }
                      return <span className='font-source text-[8px]'>{value}</span>;
                    }}
                    contentStyle={{ fontSize: '12px', color: '#fff' }}
                    wrapperStyle={{ paddingLeft: '5px' }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
          {!_.isEmpty(curSubUnitInfo) && (
            <div className='flex py-2 px-4 items-center gap-4 self-stretch rounded-[4px] bg-[#333]'>
              <div className='felx items-center gap-2 self-stretch w-[120px]'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('review.arrayIndex')}
                </span>
                <Select
                  style={{ width: '45px' }}
                  popupMatchSelectWidth={false}
                  options={_.map(curSubUnitInfo, su => ({
                    value: su.array_index,
                    label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                      # {su.array_index}
                    </span>,
                  }))}
                />
              </div>
              <div className='flex items-center gap-2 self-stretch w-full'>
                {!_.isEmpty(_.get(_.find(curSubUnitInfo, su => su.array_index === selectedArrayIndex), 'serial_number', '')) ? 
                  <Fragment>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('review.subBoardSN')}
                    </span>
                    <span className='font-source text-[12px] font-normal leading-[150%] w-full overflow-hidden overflow-ellipsis'>
                      {_.get(_.find(curSubUnitInfo, su => su.array_index === selectedArrayIndex), 'serial_number', '')}
                    </span>
                  </Fragment>
                : (
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('review.subBoardSNNotFound')}
                    </span>
                  )
                }
              </div>
            </div>
          )}
          {/* <div className='flex py-2 px-4 items-center gap-2 self-stretch rounded-[4px] bg-[#333]'>

          </div> */}

        </div>
      </div>
    </div>
  );
};

export default Live;