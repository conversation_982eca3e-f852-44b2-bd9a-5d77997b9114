import React, { useEffect, useState, useMemo } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useLazyGetAgentParamStatsQuery } from '../services/inference';
import { useTranslation } from 'react-i18next';
import { XAxis, YAxis, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import _ from 'lodash';
import { ConfigProvider, Tabs } from 'antd';
import { agentParamStatsList, leadInspection2D, solderInspection2D, solderValidRatioRange, validRatioList } from '../common/const';


const AgentParamStats = (props) => {
  const {
    isOpened,
    setIsOpened,
    selectedFeatureType,
    goldenProductId,
    selectedGroupFeatureTypeAgentParams,
    selectedScope,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
  } = props;

  const { t } = useTranslation();
  
  const [chartData, setChartData] = useState([]);
  const [activeTab, setActiveTab] = useState();
  const [tabs, setTabs] = useState([]);

  const [trigger, { data }] = useLazyGetAgentParamStatsQuery();

  useEffect(() => {
    // console.log('selectedGroupFeatureTypeAgentParams', selectedGroupFeatureTypeAgentParams);
    // console.log('selectedFeatureType', selectedFeatureType);

    if (_.isEmpty(selectedFeatureType) || _.isEmpty(selectedGroupFeatureTypeAgentParams)) {
      setTabs([]);
      setActiveTab(null);
      return;
    }

    const agents = _.filter(
      _.keys(_.get(agentParamStatsList, selectedFeatureType, {})),
      agent => _.includes(_.keys(_.get(selectedGroupFeatureTypeAgentParams, 'line_item_params', {})), agent)
    );

    // console.log('agents', agents);

    const newTabs = [];

    for (const a of agents) {
      const resultParmas = _.get(agentParamStatsList, [selectedFeatureType, a], []);
      for (const p of resultParmas) {
        if (p === validRatioList && a === solderInspection2D) {
          // see how many color user has setup
          const colorCount = _.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${a}.params.valid_ratio_ranges.param_vector`, []).length;
          for (let i = 0; i < colorCount; i++) {
            newTabs.push({
              key: `${a}.${p}.${i}`,
              label: <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t(`lineItemName.${a}`)} - {t(`agentParamName.${a}.${p}`)} #{i + 1}
              </span>,
            });
          }
        } else {
          newTabs.push({
            key: `${a}.${p}`,
            label: <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t(`lineItemName.${a}`)} - {t(`agentParamName.${a}.${p}`)}
            </span>,
          });
        }
      }
    }

    setTabs(newTabs);
    setActiveTab(_.get(newTabs, '[0].key', null));

    // console.log('newTabs', newTabs);
    // console.log('firstTab', _.get(newTabs, '[0].key', null));
  }, [
    selectedFeatureType,
    selectedGroupFeatureTypeAgentParams
  ]);

  useEffect(() => {
    
    if (isOpened && !_.isEmpty(activeTab)) {
      const query = {
        golden_product_id: goldenProductId,
        step: 0,
        has_feedback: true,
        line_item: activeTab.split('.')[0],
        agent_param_name: activeTab.split('.')[1],
      };

      if (selectedScope === 'component') {
        query.component_id = selectedCid;
      } else if (selectedScope === 'part') {
        query.part_no = selectedPartNo;
      } else if (selectedScope === 'package') {
        query.package_no = selectedPackageNo;
      }

      if (query.agent_param_name === validRatioList && query.line_item === solderInspection2D) {
        query.list_index = activeTab.split('.')[2];
      }

      trigger(query);
    }
  }, [
    isOpened,
    selectedFeatureType,
    trigger,
    goldenProductId,
    activeTab,
  ]);

  useEffect(() => {
    if (_.isEmpty(data)) return;

    const { ng_data_points: ng, ok_data_points: ok } = data;

    // random gen 500 from 0-1 for ng and good
    // const ng = _.range(0, 50).map(() => _.random(0, 1, true));
    // const ok = _.range(0, 50).map(() => _.random(0, 1, true));

    const allValues = [];

    ok.forEach((v) => {
      allValues.push({
        value: v,
        type: 'ok',
      });
    });

    ng.forEach((v) => {
      allValues.push({
        value: v,
        type: 'ng',
      });
    });

    const sortedValues = allValues.sort((a, b) => a.value - b.value);

    const result = [];

    let okIndex = 0;
    let ngIndex = 0;

    sortedValues.forEach((v) => {
      if (v.type === 'ok') {
        okIndex += 1;
      } else {
        ngIndex += 1;
      }

      const okCount = ok.length - okIndex;
      const ngCount = ngIndex;

      result.push({
        value: v.value,
        ok: okCount,
        ng: ngCount,
      });
    });

    setChartData(result);
  }, [data]);

  const recommendedInfo = useMemo(() => {
    if (_.isEmpty(chartData) || _.isEmpty(data)) return { value: null, range: null };

    const diffStart = chartData[0].ok - chartData[0].ng;
    const diffEnd = chartData[chartData.length - 1].ok - chartData[chartData.length - 1].ng;
    const hasIntersection = diffStart * diffEnd <= 0;

    if (hasIntersection) {
      const closest = chartData.reduce((prev, curr) => {
        return Math.abs(curr.ok - curr.ng) < Math.abs(prev.ok - prev.ng) ? curr : prev;
      });
      return { value: closest.value, range: null };
    }

    const okMax = _.max(data.ok_data_points);
    const ngMin = _.min(data.ng_data_points);
    return { value: null, range: [okMax, ngMin] };
  }, [chartData, data]);

  return (
    <CustomModal
      width={500}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('common.viewStats')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col p-4 items-center justify-center'>
        <div className='flex h-[42px] justify-center items-end border-b-gray-2 pt-0.5 pb-0 px-4 border-b border-solid w-[500px]'>
          <ConfigProvider
            theme={{
              components: {
                Tabs: {
                  cardPadding: '6px 18px',
                }
              }
            }}
          >
            <Tabs
              style={{ width: '500px' }}
              type='card'
              items={tabs}
              activeKey={activeTab}
              onChange={(key) => setActiveTab(key)}
            />
          </ConfigProvider>
        </div>
        {!_.isEmpty(chartData) ?
          <div className='flex w-full h-[344px] items-center justify-center'>
            <ResponsiveContainer width='100%' height={334}>
              <LineChart data={chartData}>
                <XAxis
                  dataKey='value'
                  label={{ value: t('common.aiDeviationScore'), position: 'insideBottom', offset: -5 }}
                  tickFormatter={(v) => `${v.toFixed(4)}`}
                  interval={'preserveStartEnd'}
                  tick={{ fontSize: 12 }}
                  tickCount={Math.min(10, chartData.length)}
                />
                <YAxis
                  domain={[0, Math.max(data?.ok_data_points.length || 0, data?.ng_data_points.length || 0)]}
                  tickFormatter={(v) => `${v.toFixed(0)}`}
                  label={{ value: t('common.count'), angle: -90, position: 'insideLeft', style: { textAnchor: 'middle' } }}
                  allowDecimals={false}
                />
                <Tooltip formatter={(v) => `${v}`} />
                <Line type='monotone' dataKey='ok' stroke='green'  />
                <Line type='monotone' dataKey='ng' stroke='red' />
              </LineChart>
            </ResponsiveContainer>
          </div>
          :
          <div className='flex p-4 items-center justify-center'>
            <span className='font-source text-[12px] font-normal lead ing-[150%]'>
              {t('common.noData')}
            </span>
          </div>
        }
        {/* {recommendedInfo.value !== null && (
          <div className='mt-2 text-center'>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.recommendedThresholdValue')} {recommendedInfo.value.toFixed(2)}
            </span>
          </div>
        )}
        {_.isNumber(recommendedInfo, 'range.0', null) && _.isNumber(recommendedInfo, 'range.1', null) && (
          <div className='mt-2 text-center'>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.recommendedThresholdValue')} {recommendedInfo.range[0].toFixed(2)} - {recommendedInfo.range[1].toFixed(2)}
            </span>
          </div>
        )} */}
      </div>
    </CustomModal>
  );
};

export default AgentParamStats;
