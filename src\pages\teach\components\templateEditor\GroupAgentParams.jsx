import React, { Fragment, useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import {
	Button,
	Checkbox,
	Input,
	InputNumber,
	Select,
	Slider,
	Switch,
	Tooltip,
} from 'antd';
import { LineChartOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { CustomSlider } from '../../../../common/styledComponent';
import { useDispatch, useSelector } from 'react-redux';
import { setAgentParamUserAction } from '../../../../reducer/productDefine';
import { useUpdateAgentParamsMutation, useUpdateComponentMutation, useUpdateFeatureMutation } from '../../../../services/product';
import {
	setContainerLvlLoadingMsg,
	setIsContainerLvlLoadingEnabled,
} from '../../../../reducer/setting';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import {
	agentParamUserActionTypes,
	displayableAgentParams,
	doubleSidedSliderAgentParams,
	extendedRoi,
	featureTypeToAgentNames,
	heightRange,
	lead2d3dSharedAgentParams,
	lead3DExtTop,
	leadFeatureType,
	leadInspection2D,
	leadInspection3D,
	mounting3DExtendedRoiAgentParamNames,
	mountingFeatureType,
	mountingInspection2D,
	mountingInspection3D,
	padColorCenterHue,
	padColorCenterSat,
	padColorEndHue,
	padColorEndVal,
	padColorStartHue,
	padColorStartVal,
	polarityCheckThreshold,
	polarityRoi,
	profileRoi,
	profileRoiAgentParamNames,
	solder3DExtendedRoiAgentParamNames,
	solderColorCenterHue,
	solderColorCenterSat,
	solderColorEndHue,
	solderColorEndVal,
	solderColorStartHue,
        solderColorStartVal,
        solderFeatureType,
        solderInspection3D,
        solderInspection2D,
  solderValidRatioRange,
        liftedLeadTipValidRatioRange,
        tipColorCenterHue,
        tipColorCenterSat,
        tipColorEndHue,
        tipColorEndVal,
        tipColorStartHue,
        tipColorStartVal,
        agentParamStatsList,
                                aoiVersion,
                                isAOI2DSMT,
        mmValueDecimal,
        angleValueDecimal,
        mmAgentParamsNames,
        angleAgentParamsNames,
				leadInspection2DBase,
				lead2dBaseSharedAgentParams,
				extTop,
				extBot,
				leadGapFeatureType,
} from '../../../../common/const';
import { getColorByStr, is2DLeadColorAgentParams, is2DLeadRangeAgentParams, is2DSolderColorAgentParams, orderAgentParams, twoDLeadGapAgentParamCheck } from '../../../../common/util';
import { text } from '../../../../common/translation';
import { t, use } from 'i18next';
import {
	getComponentCenterByRoiDtoObj,
	getComponentRectInfoByFeatures,
	rotatePoint,
} from '../../../../viewer/util';
import HSVColorRangePicker from '../../../../modal/HSVColorRangePicker';
import Solder2DColorRangeParams from './Solder2DColorRangeParams';
import AgentParamStats from '../../../../modal/AgentParamStats';
import './CustomSlider.css';
import { systemApi } from '../../../../services/system';
import Lead2DAgentParams from './Lead2DAgentParams';

const GroupAgentParams = (props) => {
	const {
		agentObj,
    updateAllFeaturesState,
    lintItemName,
    setSelectedAgentParam,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    selectedGroupFeatureTypeAgentParams,
    handleRefetchSelectedGroupAgentParams,
    goldenProductId,
    selectedFeatureType,
    allFeatures,
    setSelectedGroupFeatureTypeAgentParams,
		allComponents,
		refetchAllComponents,
	} = props;

	const { t } = useTranslation();

	const [statsQuery, setStatsQuery] = useState(null);
	const [isAgentParamStatsOpen, setIsAgentParamStatsOpen] = useState(false);
	const { data: systemMetadata } = useSelector((state) =>
					systemApi.endpoints.getSystemMetadata.select()(state)
	);

  const [updateAgentParamsInGroup] = useUpdateAgentParamsMutation();
	const [updateComponent] = useUpdateComponentMutation();

	const isMounting3DExtendedRoiAgent = (lintItemName, agentParamName) => {
		if (lintItemName !== mountingInspection3D) return false;
		return _.includes(mounting3DExtendedRoiAgentParamNames, agentParamName);
	};

	const isSolderExtendedRoiAgent = (lintItemName, agentParamName) => {
		if (lintItemName !== solderInspection3D) return false;
		return _.includes(solder3DExtendedRoiAgentParamNames, agentParamName);
	};

	const isProfileRoiAgent = (agentParamName) => {
		return _.includes(profileRoiAgentParamNames, agentParamName);
	};

	const isMounting3DExtendedRoiEnabled = (agentObj, lineItemName) => {
		if (lineItemName !== mountingInspection3D) return false;
		for (const agentParamName of mounting3DExtendedRoiAgentParamNames) {
			if (!_.get(agentObj, `params.${agentParamName}.active`, false))
				return false;
		}
		return true;
	};

	const isSolder3DEnabled = (selectedFeature) => {
		return _.get(
			selectedFeature,
			`line_item_params.${solderInspection3D}.enabled`,
			false
		);
	};

	const submit = (
    newGroupObj,
    lineItemName,
    agentParamName,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
  ) => {
		const run = async (
			newGroupObj,
			lineItemName,
			agentParamName,
      selectedCid,
      selectedPartNo,
      selectedPackageNo,
      selectedScope,
      goldenProductId,
      selectedFeatureType,
			// allFeatures,
			// allComponents,
    ) => {
			// if agent is lead2d/3d's shared param then also update the other lead dimension's param
			let modifiedGroupObj = _.cloneDeep(newGroupObj);
			if (
				!isAOI2DSMT &&
				(lineItemName === leadInspection2DBase ||
				lineItemName === leadInspection3D)
			) {
				if (_.includes(lead2d3dSharedAgentParams, agentParamName)) {
					const otherLineItemName =
						lineItemName === leadInspection2DBase
							? leadInspection3D
							: leadInspection2DBase;
              modifiedGroupObj = _.set(
						modifiedGroupObj,
						`line_item_params.${otherLineItemName}.params.${agentParamName}`,
						_.get(
							modifiedGroupObj,
							`line_item_params.${lineItemName}.params.${agentParamName}`
						)
					);
				}
			}

			if (
				isAOI2DSMT &&
				(lineItemName === leadInspection2D ||
				lineItemName === leadInspection2DBase)
			) {
				if (_.includes(lead2dBaseSharedAgentParams, agentParamName)) {
					const otherLineItemName =
						lineItemName === leadInspection2D
							? leadInspection2DBase
							: leadInspection2D;
              modifiedGroupObj = _.set(
						modifiedGroupObj,
						`line_item_params.${otherLineItemName}.params.${agentParamName}`,
						_.get(
							modifiedGroupObj,
							`line_item_params.${lineItemName}.params.${agentParamName}`
						)
					);
				}
			}

      const payload = {
        line_item_params: modifiedGroupObj.line_item_params,
        product_id: goldenProductId,
        step: 0,
        feature_type: selectedFeatureType === leadGapFeatureType ? leadFeatureType : selectedFeatureType,
      };

      if (_.isInteger(selectedCid) && selectedScope === 'component') {
        payload.component_id = selectedCid;
      } else if (!_.isEmpty(selectedPartNo) && selectedScope === 'part') {
        payload.part_no = selectedPartNo;
      } else if (!_.isEmpty(selectedPackageNo) && selectedScope === 'package') {
        payload.package_no = selectedPackageNo;
      }

			const res = await updateAgentParamsInGroup(payload);

			if (res.error) {
				aoiAlert(
					t('notification.error.updateFeature'),
					ALERT_TYPES.COMMON_ERROR
				);
				console.error('update feature failed', res.error.message);
				return;
			}

			await updateAllFeaturesState(_.get(res, 'data.feature_ids', []), 'updateGroupParam', _.get(res, 'data.line_item_params', {}));

			// in some cases component neeeds to be updated
			if (
				!_.isEmpty(_.get(res, 'data.feature_ids', [])) &&
				(
					(agentParamName === lead3DExtTop && lineItemName === leadInspection3D) ||
					(_.includes([extTop, extBot], agentParamName) && lineItemName === leadInspection2D)
				)
			) {
				// we can just find one feature from the update response
				const sampleFid = _.get(res, 'data.feature_ids[0]');

				const sampleFeature = _.find(allFeatures, f => f.feature_id === sampleFid);
				const sampleComponent = _.find(allComponents, c => c.region_group_id === sampleFeature.group_id && c.array_index === sampleFeature.array_index);

				// find all features in the same component
				// replace the sample feature with the updated sample feature dto
				const relatedFeatures = _.map(
					_.filter(
						allFeatures,
						f => f.group_id === sampleFeature.group_id && f.array_index === sampleFeature.array_index
					),
					f => {
						if (f.feature_type === sampleFeature.feature_type) {
							return {
								...f,
								line_item_params: _.get(res, 'data.line_item_params', {}),
							};
						} return f;
					}
				);

				const componentRectInfo = getComponentRectInfoByFeatures(
					relatedFeatures,
					sampleComponent,
				);

				const cPayload = {
					...sampleComponent,
					center: componentRectInfo.center,
					shape: {
						type: 'obb',
						points: [
							componentRectInfo.pMin,
							componentRectInfo.pMax,
						],
						center: null,
						angle: 0,
					},
				};

				delete cPayload['color_map_uri'];
				delete cPayload['depth_map_uri'];
				delete cPayload['created_at'];
				delete cPayload['modified_at'];
				delete cPayload['can_group_by_package_no'];
				delete cPayload['can_group_by_part_no'];
				delete cPayload['array_index'];
				delete cPayload['cloned'];
				delete cPayload['designator'];
				delete cPayload['variation_for'];
				delete cPayload['all'];

				const res1 = await updateComponent({
					body: cPayload,
					params: { allComponents: true },
				});

				if (res1.error) {
					aoiAlert(
						t('notification.error.updateComponent'),
						ALERT_TYPES.COMMON_ERROR
					);
					console.error('update component failed', res1.error.message);
					return;
				}

				await refetchAllComponents();
			}

      await handleRefetchSelectedGroupAgentParams(
        selectedFeatureType,
        selectedCid,
        selectedPartNo,
        selectedPackageNo,
        goldenProductId,
        selectedScope,
      );
		};

		run(
      newGroupObj,
      lineItemName,
      agentParamName,
      selectedCid,
      selectedPartNo,
      selectedPackageNo,
      selectedScope,
      goldenProductId,
      selectedFeatureType,
			// allFeatures,
			// allComponents,
    );
	};

  if (_.isEmpty(_.get(agentObj, 'params', {}))) return;

	if (lintItemName === leadInspection2D) {
		return (
			<Fragment>
				{/* <AgentParamStats
					isOpened={isAgentParamStatsOpen}
					setIsOpened={setIsAgentParamStatsOpen}
					query={statsQuery}
					selectedFeatureType={selectedFeatureType}
				/> */}
				<Lead2DAgentParams
					isAgentParamStatsOpen={isAgentParamStatsOpen}
					setIsAgentParamStatsOpen={setIsAgentParamStatsOpen}
					statsQuery={statsQuery}
					setStatsQuery={setStatsQuery}
          selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
					lintItemName={lintItemName}
          selectedFeatureType={selectedFeatureType}
					submit={submit}
					selectedCid={selectedCid}
          selectedPartNo={selectedPartNo}
          selectedPackageNo={selectedPackageNo}
          selectedScope={selectedScope}
          goldenProductId={goldenProductId}
          updateAllFeaturesState={updateAllFeaturesState}
          setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
					agentObj={agentObj}
				/>
			</Fragment>
		);
	}

	return (
		<Fragment>
			{/* <AgentParamStats
				isOpened={isAgentParamStatsOpen}
				setIsOpened={setIsAgentParamStatsOpen}
				query={statsQuery}
			/> */}
		<div
      className="flex flex-1 flex-col self-stretch py-2 px-4 gap-4"
    >
			{_.map(
				_.orderBy(
          _.keys(_.get(agentObj, 'params', {})),
          (o) => {
            // order by alphabetically
            // return t(`agentParamName.${agentObj.name}.${o}`);
            return orderAgentParams(o);
          }
        ),
				(agentParamName, id) => {
				if (
					!isMounting3DExtendedRoiAgent(lintItemName, agentParamName) &&
					!isProfileRoiAgent(agentParamName) &&
					!isSolderExtendedRoiAgent(lintItemName, agentParamName) &&
          !is2DLeadColorAgentParams(lintItemName, agentParamName) &&
					!is2DLeadRangeAgentParams(lintItemName, agentParamName) &&
					!is2DSolderColorAgentParams(lintItemName, agentParamName) &&
					(selectedFeatureType === leadGapFeatureType ? twoDLeadGapAgentParamCheck(lintItemName, agentParamName) : true) &&
					(selectedFeatureType === leadFeatureType ? agentParamName !== 'bridge_threshold' : true) &&
					!(_.startsWith(selectedFeatureType, '_text') && agentParamName === 'enable_rotation')
				) {
					return (
						<div
							className="grid self-stretch items-center gap-1"
							style={{ gridTemplateColumns: '8% 30% 62%' }}
							key={id}
						>
							<div className="flex items-center self-stretch">
								{!_.get(agentObj, `params.${agentParamName}.required`, false) && agentParamName !== polarityRoi &&
									<Checkbox
										checked={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
										onClick={() => {
											let newGroupObj = _.cloneDeep(selectedGroupFeatureTypeAgentParams);
											newGroupObj = _.set(
												newGroupObj,
												`line_item_params.${lintItemName}.params.${agentParamName}.active`,
												!_.get(agentObj, `params.${agentParamName}.active`, false)
											);

											// polarity roi and polarity check threshold's enable status are binded
											if (lintItemName === mountingInspection2D && _.includes([polarityCheckThreshold, polarityRoi], agentParamName)) {
												if (agentParamName === polarityCheckThreshold) {
													newGroupObj = _.set(
														newGroupObj,
														`line_item_params.${lintItemName}.params.${polarityRoi}.active`,
														!_.get(agentObj, `params.${agentParamName}.active`, false)
													);
												} else if (agentParamName === polarityRoi) {
													newGroupObj = _.set(
														newGroupObj,
														`line_item_params.${lintItemName}.params.${polarityCheckThreshold}.active`,
														!_.get(agentObj, `params.${agentParamName}.active`, false)
													);
												}
											}

											submit(
                        newGroupObj,
                        lintItemName,
                        agentParamName,
                        selectedCid,
                        selectedPartNo,
                        selectedPackageNo,
                        selectedScope,
                        goldenProductId,
                        selectedFeatureType,
                      );
										}}
									/>
								}
							</div>
							<div className="flex items-center self-stretch gap-2 ">
								<div className="flex items-center gap-1 w-full">
									<span
										className="font-source text-[12px] font-normal leading-[150%] pt-0.5 w-full overflow-hidden whitespace-nowrap text-ellipsis"
										title={t(`agentParamName.${agentObj.name}.${agentParamName}`)}
									>
										{t(`agentParamName.${agentObj.name}.${agentParamName}`)}
									</span>
									{!_.isEmpty(
										_.get(
											text,
											`agentParamDesc.${lintItemName}.${agentParamName}`
										)
									) && (
										<Tooltip
											title={<span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t(
												`agentParamDesc.${lintItemName}.${agentParamName}`
											)}
                      </span>}
										>
											<img
															src="/icn/info_white.svg"
															alt="info"
															className="w-3 h-3"
															/>
										</Tooltip>
									)}
									{/* {selectedScope !== 'component' && */}
									{/* { _.includes(
											_.get(agentParamStatsList, lintItemName, []),
											agentParamName
									) &&
											_.get(agentObj, `params.${agentParamName}.active`, false) &&
									(
											<Tooltip
												title={<span className='font-source text-[12px] font-normal leading-[150%]'>
													{t('common.viewStats')}
												</span>}
											>
												<Button
													size='small'
													onClick={(e) => {
														e.stopPropagation();
														const query = {
															golden_product_id: goldenProductId,
															step: 0,
															has_feedback: true,
															line_item: lintItemName,
															agent_param_name: agentParamName,
														};
														if (selectedScope === 'component') {
															query.component_id = selectedCid;
														} else if (selectedScope === 'part') {
															query.part_no = selectedPartNo;
														} else if (selectedScope === 'package') {
															query.package_no = selectedPackageNo;
														}
														setStatsQuery(query);
														setIsAgentParamStatsOpen(true);
													}}
												>
													<LineChartOutlined
														className='w-3 h-3'
													/>
												</Button>
											</Tooltip>
										)
									} */}
								</div>
								{_.includes(
									displayableAgentParams,
									`${lintItemName}.${agentParamName}`
								) && (
									<div
										className="flex w-4 h-4 border-[1px] border-gray-4 rounded-[2px]"
										style={{
											background: getColorByStr(
												`${lintItemName}.${agentParamName}`
											),
										}}
									/>
								)}
							</div>
							<div className='flex items-center self-stretch gap-1 w-full'>
								{lintItemName === mountingInspection2D && _.includes(['threshold', 'defect_check_threshold', 'polarity_check_threshold'], agentParamName) &&
									<span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 whitespace-nowrap'>
										{t('common.threshold')}
									</span>
								}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_range`, {})
								) &&
									!_.includes(
										doubleSidedSliderAgentParams,
										`${lintItemName}.${agentParamName}`
									) && (
										<AgentParamRange
											fieldInfo={_.get(
												agentObj,
												`params.${agentParamName}.param_range`,
												{}
											)}
											lintItemName={lintItemName}
											agentParamName={agentParamName}
											submit={submit}
											active={_.get(
												agentObj,
												`params.${agentParamName}.active`,
												false
											)}
                      selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                      selectedCid={selectedCid}
                      selectedPartNo={selectedPartNo}
                      selectedPackageNo={selectedPackageNo}
                      selectedScope={selectedScope}
                      goldenProductId={goldenProductId}
                      selectedFeatureType={selectedFeatureType}
										/>
									)}
								{_.includes(
									doubleSidedSliderAgentParams,
									`${lintItemName}.${agentParamName}`
								) && (
									<DoubleSidedSlider
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_range`,
											{}
										)}
										lintItemName={lintItemName}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
                    selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedPackageNo={selectedPackageNo}
                    selectedScope={selectedScope}
                    goldenProductId={goldenProductId}
                    selectedFeatureType={selectedFeatureType}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_roi`, {})
								) && (
									<AgentParamRoi
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_roi`,
											{}
										)}
										agentParamName={agentParamName}
										lintItemName={lintItemName}
										setSelectedAgentParam={setSelectedAgentParam}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_int`, {})
								) && (
									<AgentParamInt
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_int`,
											{}
										)}
										lintItemName={lintItemName}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
                    selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedPackageNo={selectedPackageNo}
                    selectedScope={selectedScope}
                    goldenProductId={goldenProductId}
                    selectedFeatureType={selectedFeatureType}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_float`, {})
								) && (
									<AgentParamFloat
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_float`,
											{}
										)}
										lintItemName={lintItemName}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
                    selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedPackageNo={selectedPackageNo}
                    selectedScope={selectedScope}
                    goldenProductId={goldenProductId}
                    selectedFeatureType={selectedFeatureType}
									/>
								)}
								{_.isBoolean(
									_.get(agentObj, `params.${agentParamName}.param_bool`, false)
								) && (
									<AgentParamBool
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_bool`,
											false
										)}
										lintItemName={lintItemName}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
                    selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedPackageNo={selectedPackageNo}
                    selectedScope={selectedScope}
                    goldenProductId={goldenProductId}
                    selectedFeatureType={selectedFeatureType}
									/>
								)}
								{_.isString(
									_.get(agentObj, `params.${agentParamName}.param_string`, false)
								) && (
									<AgentParamString
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_string`,
											false
										)}
										lintItemName={lintItemName}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
                    selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedPackageNo={selectedPackageNo}
                    selectedScope={selectedScope}
                    goldenProductId={goldenProductId}
                    selectedFeatureType={selectedFeatureType}
									/>
								)}
								{!_.isEmpty(
									_.get(agentObj, `params.${agentParamName}.param_enum`, {})
								) && (
									<AgentParamEnum
										fieldInfo={_.get(
											agentObj,
											`params.${agentParamName}.param_enum`,
											{}
										)}
										lintItemName={lintItemName}
										agentParamName={agentParamName}
										submit={submit}
										active={_.get(
											agentObj,
											`params.${agentParamName}.active`,
											false
										)}
                    selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
                    selectedCid={selectedCid}
                    selectedPartNo={selectedPartNo}
                    selectedPackageNo={selectedPackageNo}
                    selectedScope={selectedScope}
                    goldenProductId={goldenProductId}
                    selectedFeatureType={selectedFeatureType}
									/>
								)}
							</div>
						</div>
					);
				}
			})}
			{lintItemName === solderInspection2D && isAOI2DSMT && (
							<Solder2DColorRangeParams
											selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
											lintItemName={lintItemName}
											selectedCid={selectedCid}
											selectedPartNo={selectedPartNo}
											selectedPackageNo={selectedPackageNo}
											selectedScope={selectedScope}
											goldenProductId={goldenProductId}
											selectedFeatureType={selectedFeatureType}
											updateAllFeaturesState={updateAllFeaturesState}
											setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
							/>
			)}
                        {lintItemName !== solderInspection2D && selectedFeatureType === solderFeatureType && isSolder3DEnabled(selectedGroupFeatureTypeAgentParams) && (
                                <Fragment>
                                        <ExtendedRoi
						extLeft={_.get(agentObj, `params.ext_left`, {})}
						extRight={_.get(agentObj, `params.ext_right`, {})}
						extTop={_.get(agentObj, `params.ext_top`, {})}
						extBottom={_.get(agentObj, `params.ext_bottom`, {})}
						lintItemName={lintItemName}
						setSelectedAgentParam={setSelectedAgentParam}
					/>
					<ProfileRoi
						extWidth={_.get(agentObj, `params.profile_width`, {})}
						extHeight={_.get(agentObj, `params.profile_height`, {})}
						lintItemName={lintItemName}
						setSelectedAgentParam={setSelectedAgentParam}
						updateAllFeaturesState={updateAllFeaturesState}
					/>
				</Fragment>
			)}
			{selectedFeatureType === mountingFeatureType && lintItemName === mountingInspection3D && (
				<ExtendedRoi
					extLeft={_.get(agentObj, `params.ext_left`, {})}
					extRight={_.get(agentObj, `params.ext_right`, {})}
					extTop={_.get(agentObj, `params.ext_top`, {})}
					extBottom={_.get(agentObj, `params.ext_bottom`, {})}
					lintItemName={lintItemName}
					setSelectedAgentParam={setSelectedAgentParam}
				/>
			)}
							</div>

		</Fragment>
	);
};

const DoubleSidedSlider = (props) => {
	const {
		fieldInfo,
		lintItemName,
		agentParamName,
		submit,
		active,
    selectedGroupFeatureTypeAgentParams,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedOkMinValue, setDisplayedOkMinValue] = useState(
		_.get(fieldInfo, 'ok_min', 0)
	);
	const [displayedOkMaxValue, setDisplayedOkMaxValue] = useState(
		_.get(fieldInfo, 'ok_max', 1)
	);
  const okMinValRef = useRef(displayedOkMinValue);
  const okMaxValRef = useRef(displayedOkMaxValue);
  const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

	const { data: systemMetadata } = useSelector((state) =>
		systemApi.endpoints.getSystemMetadata.select()(state)
	);

	let decimal = _.get(
		systemMetadata,
		`default_line_items.${lintItemName}.params.${agentParamName}.param_range.ok_min.value`,
		0.01
	);

	const decimalStr = decimal.toString();

	if (decimalStr.indexOf('.') === -1) {
		decimal = 2;
	} else {
		decimal = decimalStr.split('.')[1].length;
	}
	decimal = Math.min(decimal, 2);

	if (_.includes(mmAgentParamsNames, `${lintItemName}.${agentParamName}`)) {
		decimal = mmValueDecimal;
	} else if (
		_.includes(angleAgentParamsNames, `${lintItemName}.${agentParamName}`)
	) {
		decimal = angleValueDecimal;
	}

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_range: {
								..._.get(
									selectedGroupFeatureTypeAgentParams,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_range`
								),
								ok_min: value[0],
								ok_max: value[1],
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedOkMinValue(_.get(fieldInfo, 'ok_min', 0));
		setDisplayedOkMaxValue(_.get(fieldInfo, 'ok_max', 1));
    okMinValRef.current = _.get(fieldInfo, 'ok_min', 0);
    okMaxValRef.current = _.get(fieldInfo, 'ok_max', 1);
		selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
	}, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
      // if (okMaxValRef.current !== _.get(fieldInfo, 'ok_max', 1) || okMinValRef.current !== _.get(fieldInfo, 'ok_min', 0)) {
			if (okMaxValRef.current !== _.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_range.ok_max`, 0)) {
        const payload = getPayload(
          [okMinValRef.current, okMaxValRef.current],
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
	}, []);

	return (
		<div className="flex flex-col self-stretch w-[160px] gap-0.5">
			<div className="flex items-center gap-1">
				<div className="flex items-center gap-1">
					<span className="font-source text-[12px] font-normal leading-[150%] pt-0.5 whitespace-nowrap">
						{t('common.min')}:
					</span>
					<InputNumber
						disabled={!active}
						size="small"
						precision={decimal}
						style={{ width: '100%' }}
						controls={false}
						min={_.get(fieldInfo, 'min', -15)}
						max={_.get(fieldInfo, 'ok_max', 1)}
						value={active ? displayedOkMinValue : null}
						onChange={(value) => {
							if (value >= displayedOkMaxValue) return;
							setDisplayedOkMinValue(value);
              okMinValRef.current = value;
						}}
						onBlur={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[val, _.get(fieldInfo, 'ok_max')],
								selectedGroupFeatureTypeAgentParamsRef.current,
								lintItemName,
								agentParamName
							);
							submit(
                payload,
                lintItemName,
                agentParamName,
                selectedCid,
                selectedPartNo,
                selectedPackageNo,
                selectedScope,
                goldenProductId,
                selectedFeatureType,
              );
						}}
						onPressEnter={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[val, _.get(fieldInfo, 'ok_max')],
								selectedGroupFeatureTypeAgentParamsRef.current,
								lintItemName,
								agentParamName
							);
							submit(
                payload,
                lintItemName,
                agentParamName,
                selectedCid,
                selectedPartNo,
                selectedPackageNo,
                selectedScope,
                goldenProductId,
								selectedFeatureType,
              );
						}}
						step={0.001}
					/>
				</div>
			</div>
			<div className="flex items-center gap-1">
				<div className="flex items-center gap-1">
					<span className="font-source text-[12px] font-normal leading-[150%] pt-0.5 whitespace-nowrap">
						{t('common.max')}:
					</span>
					<InputNumber
						disabled={!active}
						size="small"
						precision={decimal}
						style={{ width: '100%' }}
						controls={false}
						min={_.get(fieldInfo, 'ok_min', 0)}
						max={_.get(fieldInfo, 'max', 15)}
						value={active ? displayedOkMaxValue : null}
						onChange={(value) => {
							if (value <= displayedOkMinValue) return;
							setDisplayedOkMaxValue(value);
              okMaxValRef.current = value;
						}}
						onBlur={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[_.get(fieldInfo, 'ok_min'), val],
								selectedGroupFeatureTypeAgentParamsRef.current,
								lintItemName,
								agentParamName
							);
							submit(
                payload,
                lintItemName,
                agentParamName,
                selectedCid,
                selectedPartNo,
                selectedPackageNo,
                selectedScope,
                goldenProductId,
								selectedFeatureType,
              );
						}}
						onPressEnter={(e) => {
							const val = Number(e.target.value);
							const payload = getPayload(
								[_.get(fieldInfo, 'ok_min'), val],
								selectedGroupFeatureTypeAgentParamsRef.current,
								lintItemName,
								agentParamName
							);
							submit(
                payload,
                lintItemName,
                agentParamName,
                selectedCid,
                selectedPartNo,
                selectedPackageNo,
                selectedScope,
                goldenProductId,
								selectedFeatureType,
              );
						}}
						step={0.001}
					/>
				</div>
			</div>
			<Slider
				disabled={!active}
				range
				step={0.001}
				styles={{
					track: { background: '#81F499' },
					rail: { background: '#F46D6D' },
				}}
  			className="custom-slider w-full"
				style={{
					marginBottom: '10px',
				}}
				// style={{ width: '100%' }}
				min={_.get(fieldInfo, 'min', -15)}
				max={_.get(fieldInfo, 'max', 15)}
				value={[
					active ? displayedOkMinValue : null,
					active ? displayedOkMaxValue : null,
				]}
				onChange={(value) => {
					if (value[0] >= value[1]) {
						return;
					}
					setDisplayedOkMinValue(value[0]);
					setDisplayedOkMaxValue(value[1]);
          okMinValRef.current = value[0];
          okMaxValRef.current = value[1];
				}}
				onChangeComplete={(value) => {
					const payload = getPayload(
						value,
            selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
						selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

// specical agent case(includes multi agent params)
const ExtendedRoi = (props) => {
	const {
		extLeft,
		extRight,
		extTop,
		extBottom,
		lintItemName,
		setSelectedAgentParam,
	} = props;

	const [enabled, setEnabled] = useState(false);

	useEffect(() => {
		if (lintItemName === mountingInspection3D) {
			setEnabled(
				_.get(extLeft, 'active', false) &&
					_.get(extRight, 'active', false) &&
					_.get(extTop, 'active', false) &&
					_.get(extBottom, 'active', false)
			);
		} else if (lintItemName === solderInspection3D) {
			setEnabled(
				_.get(extTop, 'active', false) && _.get(extBottom, 'active', false)
			);
		}
	}, [lintItemName, extLeft, extRight, extTop, extBottom]);

	return (
		<div className="grid self-stretch items-center gap-1"
			style={{ gridTemplateColumns: '8% 30% 62%' }}>
			<div className='flex items-center self-stretch'>
			</div>
			<div className="flex items-center self-stretch gap-2">
				<span
					className="font-source text-[12px] font-normal leading-[150%] pt-0.5 overflow-hidden text-ellipsis whitespace-nowrap w-[90px]"
					title={t(`agentParamName.${lintItemName}.${extendedRoi}`)}
				>
					{t(`agentParamName.${lintItemName}.${extendedRoi}`)}
				</span>
				<div
					className="flex w-4 h-4 border-[1px] border-gray-4 rounded-[2px]"
					style={{
						background: getColorByStr(`${lintItemName}.${extendedRoi}`),
					}}
				/>
			</div>
			<Button
				disabled={!enabled}
				onClick={() => {
					setSelectedAgentParam(`${lintItemName}.${extendedRoi}`);
				}}
			>
				<span className="font-source text-[12px] font-normal leading-[150%]">
					{t('common.setExtendedRoi')}
				</span>
			</Button>
		</div>
	);
};

const ProfileRoi = (props) => {
	const {
		extWidth,
		extHeight,
		lintItemName,
		setSelectedAgentParam,
	} = props;

	return (
		<div className="grid self-stretch items-center gap-1"
			style={{ gridTemplateColumns: '8% 30% 62%' }}>
			<div className='flex items-center self-stretch' />
			{/* <Checkbox
				checked={
					_.get(extWidth, 'active', false) && _.get(extHeight, 'active', false)
				}
				onClick={() => {
					const target = !(
						_.get(extWidth, 'active', false) &&
						_.get(extHeight, 'active', false)
					);
					let newFeatureObj = _.cloneDeep(featureObj);
					for (const agentParamName of profileRoiAgentParamNames) {
						newFeatureObj = _.set(
							newFeatureObj,
							`line_item_params.${lintItemName}.params.${agentParamName}.active`,
							target
						);
					}

					const submit = async (newFeatureObj) => {
						const res = await updateFeature(newFeatureObj);

						if (res.error) {
							aoiAlert(
								t('notification.error.updateFeature'),
								ALERT_TYPES.COMMON_ERROR
							);
							console.error('update feature failed', res.error.message);
							return;
						}

						// await refetchAllFeatures();
						await updateAllFeaturesState([newFeatureObj.feature_id], 'update', [newFeatureObj]);
					};

					submit(newFeatureObj);
				}}
			/> */}
			<div className="flex items-center self-stretch gap-2">
				<span
					className="font-source text-[12px] font-normal leading-[150%] pt-0.5 overflow-hidden text-ellipsis whitespace-nowrap w-[90px]"
					title={t(`agentParamName.${lintItemName}.${profileRoi}`)}
				>
					{t(`agentParamName.${lintItemName}.${profileRoi}`)}
				</span>
				<div
					className="flex w-4 h-4 border-[1px] border-gray-4 rounded-[2px]"
					style={{
						background: getColorByStr(`${lintItemName}.${profileRoi}`),
					}}
				/>
			</div>
			<Button
				disabled={
					!(
						_.get(extWidth, 'active', false) &&
						_.get(extHeight, 'active', false)
					)
				}
				onClick={() => {
					setSelectedAgentParam(`${lintItemName}.${profileRoi}`);
				}}
			>
				<span className="font-source text-[12px] font-normal leading-[150%]">
					{t('common.setProfileRoi')}
				</span>
			</Button>
		</div>
	);
};

// NOTE: not really dynamic
const AgentParamRoi = (props) => {
	const {
		fieldInfo,
		agentParamName,
		lintItemName,
		setSelectedAgentParam,
		active,
	} = props;

	const { t } = useTranslation();

	return (
		<div className="flex items-center gap-2 self-stretch">
			<Button
				disabled={!active}
				onClick={() => {
					if (_.get(fieldInfo, 'points', []).length > 0) {
						setSelectedAgentParam(`${lintItemName}.${agentParamName}`);
          }
					// else {
					// 	// submit update feature
					// 	// we init the agent param roi at the center of the feature roi and set dimension to 1/2 of the feature roi
					// 	// agent param roi's coord system is rotate by the feature roi's angle and its origin is at the rotated feature roi's top left
					// 	// and we assume the agent param roi angle = feature roi angle
					// 	let featureCenter = getComponentCenterByRoiDtoObj(
					// 		_.get(selectedFeature, 'roi', {})
					// 	);
					// 	// featureCenter = rotatePoint(featureCenter, _.get(component, 'shape.angle'), getComponentCenterByRoiDtoObj(_.get(component, 'shape', {})));
					// 	let featureTopLeft = {
					// 		x:
					// 			featureCenter.x -
					// 			(_.get(selectedFeature, 'roi.points[1].x') -
					// 				_.get(selectedFeature, 'roi.points[0].x') +
					// 				1) /
					// 				2,
					// 		y:
					// 			featureCenter.y -
					// 			(_.get(selectedFeature, 'roi.points[1].y') -
					// 				_.get(selectedFeature, 'roi.points[0].y') +
					// 				1) /
					// 				2,
					// 	};
					// 	featureTopLeft = rotatePoint(
					// 		featureTopLeft,
					// 		_.get(selectedFeature, 'roi.angle'),
					// 		featureCenter
					// 	);
					// 	// let polarityCenter = rotatePoint(featureCenter, -_.get(selectedFeature, 'roi.angle'), featureTopLeft);
					// 	let polarityCenter = _.cloneDeep(featureCenter);

					// 	const featureRoiInnerDimension = {
					// 		width:
					// 			_.get(selectedFeature, 'roi.points[1].x') -
					// 			_.get(selectedFeature, 'roi.points[0].x') +
					// 			1,
					// 		height:
					// 			_.get(selectedFeature, 'roi.points[1].y') -
					// 			_.get(selectedFeature, 'roi.points[0].y') +
					// 			1,
					// 	};

					// 	const agentParamRoi = {
					// 		type: 'obb',
					// 		points: [
					// 			{
					// 				x: _.round(
					// 					polarityCenter.x -
					// 						featureRoiInnerDimension.width / 4 -
					// 						featureTopLeft.x,
					// 					0
					// 				),
					// 				y: _.round(
					// 					polarityCenter.y -
					// 						featureRoiInnerDimension.height / 4 -
					// 						featureTopLeft.y,
					// 					0
					// 				),
					// 			},
					// 			{
					// 				x: _.round(
					// 					polarityCenter.x +
					// 						featureRoiInnerDimension.width / 4 -
					// 						featureTopLeft.x,
					// 					0
					// 				),
					// 				y: _.round(
					// 					polarityCenter.y +
					// 						featureRoiInnerDimension.height / 4 -
					// 						featureTopLeft.y,
					// 					0
					// 				),
					// 			},
					// 		],
					// 		center: null,
					// 		angle: _.get(selectedFeature, 'roi.angle', 0),
					// 	};

					// 	let payload = _.cloneDeep(selectedFeature);
					// 	payload = _.set(
					// 		payload,
					// 		`line_item_params.${lintItemName}.params.${agentParamName}.param_roi`,
					// 		agentParamRoi
					// 	);

					// 	const submit = async (payload) => {
					// 		const res = await updateFeature(payload);

					// 		if (res.error) {
					// 			aoiAlert(
					// 				t('notification.error.updateFeature'),
					// 				ALERT_TYPES.COMMON_ERROR
					// 			);
					// 			console.error('update feature failed', res.error.message);
					// 			return;
					// 		}

					// 		setSelectedAgentParam(`${lintItemName}.${agentParamName}`);
					// 		// await refetchAllFeatures();
					// 		await updateAllFeaturesState(
					// 			[payload.feature_id],
					// 			'update',
					// 			[payload]
					// 		);
					// 	};

					// 	submit(payload);
					// }
				}}
			>
				<span className="font-source text-[12px] font-normal leading-[150%]">
					{t('common.setRoi')}
				</span>
			</Button>
		</div>
	);
};

const AgentParamRange = (props) => {
	const {
		fieldInfo,
		lintItemName,
		agentParamName,
		submit,
		active,
    selectedGroupFeatureTypeAgentParams,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(
		_.round(_.get(fieldInfo, 'ok_max', 0), 2)
	);

  const okMaxValRef = useRef(displayedValue);
	const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

	const { data: systemMetadata } = useSelector((state) =>
		systemApi.endpoints.getSystemMetadata.select()(state)
	);

	let decimal = _.get(
		systemMetadata,
		`default_line_items.${lintItemName}.params.${agentParamName}.param_range.ok_max`,
		0.01
	);

	const decimalStr = decimal.toString();

	if (decimalStr.indexOf('.') === -1) {
		decimal = 2;
	} else {
		decimal = decimalStr.split('.')[1].length;
	}
	decimal = Math.min(decimal, 2);

	if (_.includes(mmAgentParamsNames, `${lintItemName}.${agentParamName}`)) {
		decimal = mmValueDecimal;
	} else if (
		_.includes(angleAgentParamsNames, `${lintItemName}.${agentParamName}`)
	) {
		decimal = angleValueDecimal;
	}

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_range: {
								..._.get(
									selectedGroupFeatureTypeAgentParams,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_range`
								),
								ok_max: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.round(_.get(fieldInfo, 'ok_max', 0), 2));
    okMaxValRef.current = _.round(_.get(fieldInfo, 'ok_max', 0), 2);
		selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
	}, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
      if (okMaxValRef.current !== _.round(_.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_range.ok_max`, 0), 2)) {
        const payload = getPayload(
          okMaxValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

	return (
		<div className="flex flex-1 self-stretch w-full">
			<div className='flex flex-1 self-stretch items-center gap-1'>
				<InputNumber
					disabled={!active}
					style={{ width: '100%', height: '26px' }}
					controls={false}
					min={_.get(fieldInfo, 'min', 0)}
					max={_.get(fieldInfo, 'max', 1)}
					precision={decimal + 1}
					value={active ? displayedValue : null}
					onChange={(value) => {
						setDisplayedValue(value);
						okMaxValRef.current = value;
					}}
					onBlur={(e) => {
						const val = Number(e.target.value);
						const payload = getPayload(
							val,
							selectedGroupFeatureTypeAgentParamsRef.current,
							lintItemName,
							agentParamName
						);
						submit(
              payload,
              lintItemName,
              agentParamName,
              selectedCid,
              selectedPartNo,
              selectedPackageNo,
              selectedScope,
              goldenProductId,
              selectedFeatureType,
            );
					}}
					onPressEnter={(e) => {
						const val = Number(e.target.value);
						const payload = getPayload(
							val,
							selectedGroupFeatureTypeAgentParamsRef.current,
							lintItemName,
							agentParamName
						);
						submit(
              payload,
              lintItemName,
              agentParamName,
              selectedCid,
              selectedPartNo,
              selectedPackageNo,
              selectedScope,
              goldenProductId,
              selectedFeatureType,
            );
					}}
					step={0.01}
				/>
			</div>
			<div className='flex items-center self-stretch flex-1 gap-2'>
			<Slider
				disabled={!active}
				step={Math.pow(100, -decimal)}
				styles={{
					track: { background: '#81F499' },
					rail: { background: '#F46D6D' },
				}}
				style={{
					marginBottom: '10px',
					width: '100%',
					}}
					className="custom-slider"
				// style={{ width: '100%' }}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          okMaxValRef.current = value;
				}}
				onChangeComplete={(value) => {
					const payload = getPayload(
						value,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
			</div>
		</div>
	);
};

const AgentParamInt = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedGroupFeatureTypeAgentParams,
		agentParamName,
		submit,
		active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(
		_.get(fieldInfo, 'value', 0)
	);

  const displayedValRef = useRef(displayedValue);
	const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_int: {
								..._.get(
									selectedGroupFeatureTypeAgentParams,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_int`
								),
								value: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.get(fieldInfo, 'value', 0));
    displayedValRef.current = _.get(fieldInfo, 'value', 0);
		selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
	}, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
			if (displayedValRef.current !== _.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_int.value`, 0)) {
        const payload = getPayload(
          displayedValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

	return (
		<div className="flex flex-col self-stretch w-full">
			<InputNumber
				disabled={!active}
				step={1}
				style={{ width: '100%' }}
				controls={false}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          displayedValRef.current = value;
				}}
				onBlur={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
				onPressEnter={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

const AgentParamFloat = (props) => {
        const {
                fieldInfo,
                lintItemName,
                selectedGroupFeatureTypeAgentParams,
                agentParamName,
                submit,
                active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
        } = props;

				const { data: systemMetadata } = useSelector((state) =>
                systemApi.endpoints.getSystemMetadata.select()(state)
        );

        let decimal = _.get(
                systemMetadata,
                `default_line_items.${lintItemName}.params.${agentParamName}.param_float.value`,
                0.01
        );

				const decimalStr = decimal.toString();

				if (decimalStr.indexOf('.') === -1) {
					decimal = 2;
				} else {
					decimal = decimalStr.split('.')[1].length;
				}

        if (_.includes(mmAgentParamsNames, `${lintItemName}.${agentParamName}`)) {
                decimal = mmValueDecimal;
        } else if (
                _.includes(angleAgentParamsNames, `${lintItemName}.${agentParamName}`)
        ) {
                decimal = angleValueDecimal;
        }

        const [displayedValue, setDisplayedValue] = useState(
                _.round(_.get(fieldInfo, 'value', 0), decimal)
        );
  const displayedValRef = useRef(displayedValue);
	const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_float: {
								..._.get(
									selectedGroupFeatureTypeAgentParams,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_float`
								),
								value: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

        useEffect(() => {
                setDisplayedValue(_.round(_.get(fieldInfo, 'value', 0), decimal));
    displayedValRef.current = _.round(_.get(fieldInfo, 'value', 0), decimal);
                selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
        }, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
                        if (displayedValRef.current !== _.round(_.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_float.value`, 0), decimal)) {
        const payload = getPayload(
          displayedValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

	return (
		<div className="flex flex-col self-stretch w-full">
			<InputNumber
				disabled={!active}
				step={Math.pow(10, -decimal)}
				precision={decimal}
				style={{ width: '100%' }}
				controls={false}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          displayedValRef.current = value;
				}}
				onBlur={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
				onPressEnter={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

const AgentParamBool = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedGroupFeatureTypeAgentParams,
		agentParamName,
		submit,
		active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(fieldInfo);

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_bool: value,
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(fieldInfo);
	}, [fieldInfo]);

	return (
		<div className="flex flex-col self-stretch">
			<Switch
				disabled={!active}
				style={{ width: '32px' }}
				size="small"
				checked={displayedValue}
				onChange={(value) => {
					setDisplayedValue(value);
					const payload = getPayload(
						value,
						selectedGroupFeatureTypeAgentParams,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

const AgentParamString = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedGroupFeatureTypeAgentParams,
		agentParamName,
		submit,
		active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(fieldInfo);
  const displayedValueRef = useRef(fieldInfo);
  const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_string: value,
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(fieldInfo);
    displayedValueRef.current = fieldInfo;
		selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
	}, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
      // prevent user click out of the input and onBlur won't be triggered
      // if (displayedValueRef.current !== fieldInfo) {
			if (displayedValueRef.current !== _.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_string`, '')) {
        const payload = getPayload(
          displayedValueRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
				console.log('submit');
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    };
  }, []);

	return (
		<div className="flex flex-col self-stretch w-full">
			<Input
				disabled={!active}
				style={{ width: '100%' }}
				value={active ? displayedValue : null}
				onChange={(e) => {
					setDisplayedValue(e.target.value);
          displayedValueRef.current = e.target.value;
				}}
				onBlur={(e) => {
					const val = e.target.value;
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
				onPressEnter={(e) => {
					const val = e.target.value;
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

const AgentParamEnum = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedGroupFeatureTypeAgentParams,
		agentParamName,
		submit,
		active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(
		_.get(fieldInfo, 'value', '')
	);

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_enum: {
								..._.get(
									selectedGroupFeatureTypeAgentParams,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_enum`
								),
								value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.get(fieldInfo, 'value', ''));
	}, [fieldInfo]);

	return (
		<div className="flex flex-col self-stretch">
			<Select
				disabled={!active}
				style={{ width: '100%' }}
				options={_.map(_.get(fieldInfo, 'options', []), (o) => {
					return {
						label: (
							<span className="font-source text-[12px] font-normal leading-[150%]">
								{o}
							</span>
						),
						value: o,
					};
				})}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
					const payload = getPayload(
						value,
						selectedGroupFeatureTypeAgentParams,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

export default GroupAgentParams;