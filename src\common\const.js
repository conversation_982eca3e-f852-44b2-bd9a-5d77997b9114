import CalendarLocale from 'rc-picker/lib/locale/zh_CN';
import _ from 'lodash';

// environment variables
export const aoiVersion = import.meta.env.VITE_AOI_VERSION || '3d_smt';
export const isAOI2DSMT = aoiVersion === '2d_smt';
export const isAOI3DSMT = aoiVersion === '3d_smt';
export const conveyorNum = Number(import.meta.env.VITE_CONVEYOR_NUM || 2);


export const serverHost =  localStorage.getItem('serverHost') || 'http://localhost:8000';

export const fieldConstraints = {
  login: {
    username: {
      maxLength: 50,
      minLength: 3,
    },
    password: {
      maxLength: 50,
      minLength: 8,
    },
  },
  productDefine: {
    productName: {
      maxLength: 150,
      minLength: 1,
    },
    conveyorWidth: {
      max: 350,
      min: 50,
    },
  },
};

export const userRoles = {
  admin: 'ADMIN',
  operator: 'OPERATOR',
  programmer: 'PROGRAMMER',
};

export const conveyorControllerWidth = 84;

export const productDefineLeftNavWidth = 124;

export const cameraModel = {
  ad: 'AD',
  as: 'AS',
  aq: 'AQ',
  file: 'File',
};

export const customZhCNDatePickerLocale = {
  lang: {
    placeholder: '请选择日期',
    yearPlaceholder: '请选择年份',
    quarterPlaceholder: '请选择季度',
    monthPlaceholder: '请选择月份',
    weekPlaceholder: '请选择周',
    rangePlaceholder: ['开始日期', '结束日期'],
    rangeYearPlaceholder: ['开始年份', '结束年份'],
    rangeMonthPlaceholder: ['开始月份', '结束月份'],
    rangeQuarterPlaceholder: ['开始季度', '结束季度'],
    rangeWeekPlaceholder: ['开始周', '结束周'],
    "shortMonths": [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月"
    ],
    "shortWeekDays": [
      "日",
      "一",
      "二",
      "三",
      "四",
      "五",
      "六"
    ],
    ...CalendarLocale,
  },
  timePickerLocale: {
    placeholder: '请选择时间',
  },
  "dateFormat": "YYYY-MM-DD",
  "dateTimeFormat": "YYYY-MM-DD HH:mm:ss",
  "weekFormat": "YYYY-wo",
  "monthFormat": "YYYY-MM",
};

export const conveyorUsage = {
  invalid: 0,
  program: 1,
  inspection: 2,
};

export const conveyorOperation = {
  invalid: 0,
  load: 1, // load items
  unload: 2,
  skip: 3, // skip an item(pass-through)
  moveForward: 4, // move forward
  moveBackward: 5, // move backward
  stop: 6, // stop
};

export const threedViewerWindowDefaultDimension = {
  width: window.innerWidth * 0.75,
  height: window.innerHeight * 0.75,
};

export const pointCloudColorMode = {
  FULL_COLOR: 'full_color',
  GREEN_MODE: 'green_mode',
  GRAY_SCALE: 'gray_scale',
  DEPTH_MAP: 'depth_map',
};

export const define3DViewBoxLimit = {
  width: 2400,
  height: 2400,
};

export const newRectStrokeWidth = 1;

export const arrayBoardSelectionRoiStrokeWidth = 5;

export const defaultCircleRadius = 10;

export const defaultNewFeatureExtRoiPadding = {
  top: 10,
  right: 10,
  bottom: 10,
  left: 10,
};

// in pixel coord
export const componentRoiPadding = {
  top: 13,
  right: 13,
  bottom: 13,
  left: 13,
};

// 1 mega pixel = 1,000,000 pixels(1 million)
// normally chrome's limitation is 16384x16384 = 268,435,456 pixels
// we use 10 mega pixels for bg, 80 mega pixels for cropped 2d display, 20 mega pixels for each cropped 3d display window
export const highResoluBgMegaPixelCount = 1;
export const highResoluCroppedDisplayMegaPixelCount = 2;
export const highResolyCropped3DDisplayMegaPixelCount = 20;
export const maxCropped3DDisplayWindow = 4;

export const markerFeatureType = 'marker';

export const componentAttribute = {
  packageNo: 'packageNo',
  partNo: 'partNo',
  x: 'x',
  y: 'y',
  layerCol: 'layerCol',
  rotationCol: 'rotationCol',
  designator: 'designator',
};

export const highResoluRefreshInterval = 350; // for wheel scroll

export const keyboardSubmitInterval = 600;

export const uploadCADSupportedFileExtension = ['csv', 'txt', 'tsv'];

export const extendedRoiRelatedLineItems = ['polarity_roi'];

export const featureDirectionRelatedLineItems = ['feature_direction'];

export const featureROIInnerDimensionMaxLimit = 1000;

export const agentParamUserActionTypes = {
  viewRoi: 'viewRoi',
  editRoi: 'editRoi',
};

export const mountingInspection3D = 'mounting_inspection_3d';
export const mountingInspection2D = 'mounting_inspection_2d';
export const leadInspection3D = 'lead_inspection_3d';
// export const leadInspection2D = 'lead_inspection_2d';
export const leadInspection2D = 'lead_inspection_2d_v2';
export const leadInspection2DBase = 'lead_inspection_2d';
export const solderInspection3D = 'solder_inspection';
export const solderInspection2D = 'solder_inspection_2d';
export const textVerification = 'text_verification';
export const barcodeScanner = 'barcode_scanner';
export const polarityRoi = 'polarity_roi';
export const maskRoi = 'mask_roi';
export const extendedRoi = 'extended_roi'; // NOTE: this is not a agent param come from backend but a custom one for frontend display purpose
export const directionArrow = 'direction_arrow'; // NOTE: this is not a agent param come from backend but a custom one for frontend display purpose
export const profileWidth = 'profile_width';
export const profileHeight = 'profile_height';
export const leadSegmentationRects = 'lead_segmentation_rects'; // NOTE: this is not a agent param come from backend but a custom one for frontend display purpose
export const profileRoi = 'profile_roi'; // NOTE: this is not a agent param come from backend but a custom one for frontend display purpose
export const heightRange = 'height_range';
export const polarityCheckThreshold = 'polarity_check_threshold';
export const backgroundRoi1 = 'background_roi1';
export const backgroundRoi2 = 'background_roi2';
export const filletVolumeRatio = 'fillet_volume_ratio';
export const filletUpperThreshold = 'fillet_upper_threshold';
export const filletLowerThreshold = 'fillet_lower_threshold';
export const filletOpenThreshold = 'fillet_open_threshold';
export const mounting3DExtendedRoiAgentParamNames = [
  'ext_left',
  'ext_right',
  'ext_top',
  'ext_bottom',
];
export const solder3DExtendedRoiAgentParamNames = [
  'ext_top',
  'ext_bottom',
];
export const lead3DExtTop = 'ext_top';
export const extTop = 'ext_top';
export const extBottom = 'ext_bottom';
export const extBot = 'ext_bot';

export const profileRoiAgentParamNames = [
  'profile_width',
  'profile_height',
];

export const displayableAgentParams = [
  `${mountingInspection2D}.${polarityRoi}`,
  ...(isAOI2DSMT ? [] : [`${mountingInspection3D}.${extendedRoi}`]),
  `${mountingInspection2D}.${maskRoi}`,
  ...(isAOI2DSMT ? [] : [`${mountingInspection3D}.${backgroundRoi1}`]),
  ...(isAOI2DSMT ? [] : [`${mountingInspection3D}.${backgroundRoi2}`]),
];

export const displayableAgentParamsWhenComponentSelected = [
  `${mountingInspection2D}.${polarityRoi}`,
];

export const allAgentParamsRois = [
  `${mountingInspection2D}.${polarityRoi}`,
  ...(isAOI2DSMT ? [] : [`${mountingInspection3D}.${extendedRoi}`]),
];

export const doubleSidedSliderAgentParams = [
  `${mountingInspection3D}.${heightRange}`,
  `${leadInspection3D}.${heightRange}`,
];

export const lead2d3dSharedAgentParams = [
  'bridge_width_mm',
  'lead_count',
  'lead_width_mm',
  'lead_angle',
];

export const lead2dBaseSharedAgentParams = [
  'lead_count',
  'lead_width_mm',
];

export const mmValueDecimal = 2;
export const angleValueDecimal = 1;
export const mmAgentParamsNames = [
  'lead_inspection_2d.lead_width_mm',
  'lead_inspection_2d_v2.bridge_width_mm',
  'lead_inspection_2d_v2.lead_width_mm',
  'lead_inspection_3d.lead_width_mm',
];

export const angleAgentParamsNames = [
  'solder_inspection.max_component_angle',
  'solder_inspection.max_pcb_angle',
];

export const modelTypes = {
  mountingModel: 'MOUNTING_MODEL',
  leadModel: 'LEAD_MODEL',
  solderModel: 'SOLDER_MODEL',
  // for auto generate agent params useage starts
  solder3DModel: 'SOLDER_3D_MODEL',
  mounting3DModel: 'MOUNTING_3D_MODEL',
  lead3DModel: 'LEAD_3D_MODEL',
  lead2DV2Model: 'LEAD_2D_V2_MODEL',
  solder2DModel: 'SOLDER_2D_MODEL',
  // for auto generate agent params useage ends
  textVerificationModel: 'TEXT_VERIFICATION_MODEL',
  textDirectionModel: 'TEXT_DIRECTION_MODEL',
};

export const retrainModelTaskPhaseType = {
  pending: 'PENDING_PHASE',
  setUpdate: 'SET_UPDATE_PHASE',
  modelUpdate: 'MODEL_UPDATE_PHASE',
  complete: 'COMPLETE_PHASE',
  failure: 'FAILURE_PHASE',
  invalid: 'INVALID_PHASE',
};

export const mountingFeatureType = '_mount';

export const leadFeatureType = '_ic_lead';

export const leadGapFeatureType = '_ic_lead_gap'; // NOTE: this is not a agent param come from backend but a custom one for frontend display purpose

export const textVerificationFeatureType = '_text';

export const solderFeatureType = '_solder';

export const textFeatureType = '_text';

const leadFeatureAgents = isAOI2DSMT
  ? [leadInspection2D, leadInspection2DBase]
  : isAOI3DSMT
    ? [leadInspection3D, leadInspection2DBase]
    : [leadInspection2D, leadInspection3D];

const leadGapFeatureAgents = isAOI2DSMT
  ? [leadInspection2DBase]
  : isAOI3DSMT
    ? [leadInspection3D]
    : [leadInspection2D, leadInspection3D];

const solderFeatureAgents = isAOI2DSMT
  ? [solderInspection2D]
  : isAOI3DSMT
    ? [solderInspection3D]
    : [solderInspection3D, solderInspection2D];

export const featureTypeToAgentNames = {
  [mountingFeatureType]: [
    mountingInspection2D,
    ...(isAOI2DSMT ? [] : [mountingInspection3D]),
  ],
  [leadFeatureType]: leadFeatureAgents,
  [leadGapFeatureType]: leadGapFeatureAgents,
  [textVerificationFeatureType]: [textVerification],
  [solderFeatureType]: solderFeatureAgents,
};

export const trainingSetCropImgPadding = 10;

export const templateEditorLocateRectWaitTime = 50;

export const sceneCopyRectBottomRightOffset = 10;

export const viewportSupportLineDefaultColor = '#FF0000';

export const viewportSupportLineDefaultStrokeWidth = 2;

export const viewportMouseMoveUpdateSupportLineInterval = 100;

export const defaultDashedArray = [3, 3];

export const COMMON_HTTP_CODE = {
  conflict: 409,
  forbidden: 401,
};

export const localStorageKeys = {
  username: 'aoiUsername',
  accessToken: 'aoiAccessToken',
  userRole: 'aoiUserRole',
  tokenExp: 'aoiTokenExp',
  accId: 'aoiAccId',
};

export const accessTokenExpCheckInterval = 1000 * 60 * 5; // 5 minutes

export const updateFeatureInGroup = 'updateFeatureInGroup';
export const addFeatureInGroup = 'addFeatureInGroup';

export const minFeatureBoxLength = 5;

export const lead2DRangeAgentParamNames = [
  'solder_valid_ratio_range',
  'lifted_lead_tip_valid_ratio_range',
];

export const solderColorCenterHue = 'solder_color_center_hue';
export const solderColorCenterSat = 'solder_color_center_sat';
export const solderColorStartHue = 'solder_color_start_hue';
export const solderColorStartVal = 'solder_color_start_val';
export const solderColorEndHue = 'solder_color_end_hue';
export const solderColorEndVal = 'solder_color_end_val';
export const padColorCenterHue = 'pad_color_center_hue';
export const padColorCenterSat = 'pad_color_center_sat';
export const padColorStartHue = 'pad_color_start_hue';
export const padColorStartVal = 'pad_color_start_val';
export const padColorEndHue = 'pad_color_end_hue';
export const padColorEndVal = 'pad_color_end_val';
export const tipColorCenterHue = 'tip_color_center_hue';
export const tipColorCenterSat = 'tip_color_center_sat';
export const tipColorStartHue = 'tip_color_start_hue';
export const tipColorStartVal = 'tip_color_start_val';
export const tipColorEndHue = 'tip_color_end_hue';
export const tipColorEndVal = 'tip_color_end_val';

export const liftedLeadPadMeanThreshold = 'lifted_lead_pad_mean_threshold';
export const liftedLeadSolderMeanThreshold = 'lifted_lead_solder_mean_threshold';
export const liftedLeadSolderNeighborThreshold = 'lifted_lead_solder_neighbor_threshold';
export const liftedLeadTipMeanLowerThreshold = 'lifted_lead_tip_mean_lower_threshold';
export const liftedLeadTipMeanUpperThreshold = 'lifted_lead_tip_mean_upper_threshold';
export const solderMeanThreshold = 'solder_mean_threshold';
export const solderNeighborThreshold = 'solder_neighbor_threshold';

export const solderValidRatioRange = 'solder_valid_ratio_range';
export const liftedLeadTipValidRatioRange = 'lifted_lead_tip_valid_ratio_range';
export const solder2DCenterHue = 'center_hue';
export const solder2DCenterSat = 'center_sat';
export const solder2DStartHue = 'start_hue';
export const solder2DStartVal = 'start_val';
export const solder2DEndHue = 'end_hue';
export const solder2DEndVal = 'end_val';
export const solder2DValidRatioRanges = 'valid_ratio_ranges';
export const solder2DColorRanges = 'color_ranges';

export const lead2DColorAgentParamNames = [
  solderColorCenterHue,
  solderColorCenterSat,
  solderColorStartHue,
  solderColorStartVal,
  solderColorEndHue,
  solderColorEndVal,
  padColorCenterHue,
  padColorCenterSat,
  padColorStartHue,
  padColorStartVal,
  padColorEndHue,
  padColorEndVal,
  tipColorCenterHue,
  tipColorCenterSat,
  tipColorStartHue,
  tipColorStartVal,
  tipColorEndHue,
  tipColorEndVal,
  solderValidRatioRange,
  liftedLeadTipValidRatioRange,
];

// Lead 2D V2 Agent Param Groups
export const lead2DV2AgentParamGroups = {
  common: [
    'enable_visualization',
    'lead_count',
    'lead_width_mm',
    'ext_top',
    'ext_bot',
    'bridge_width_mm',
  ],
  solder: [
    'solder_valid_ratio_range',
    'solder_mean_threshold',
    'solder_neighbor_threshold'
  ],
  pad: [
    'lifted_lead_min_solder_pad_difference',
    'lifted_lead_solder_mean_threshold',
    'lifted_lead_solder_neighbor_threshold',
    'lifted_lead_pad_mean_threshold'
  ],
  tip: [
    'lifted_lead_tip_valid_ratio_range',
    'lifted_lead_tip_mean_lower_threshold',
    'lifted_lead_tip_mean_upper_threshold'
  ]
};

// Agent params that should display with % unit
export const percentageAgentParamNames = [
  'solder_mean_threshold',
  'solder_neighbor_threshold',
  'lifted_lead_solder_mean_threshold',
  'lifted_lead_solder_neighbor_threshold',
  'lifted_lead_pad_mean_threshold',
  'lifted_lead_tip_mean_lower_threshold',
  'lifted_lead_tip_mean_upper_threshold'
];

export const solder2DColorAgentParamNames = [
  solder2DCenterHue,
  solder2DCenterSat,
  solder2DStartHue,
  solder2DStartVal,
  solder2DEndHue,
  solder2DEndVal,
  solder2DValidRatioRanges,
];

export const solderValidRatioList = 'solder_valid_ratio_list';
export const padValidRatioList = 'pad_valid_ratio_list';
export const tipValidRatioList = 'tip_valid_ratio_list';
export const validRatioList = 'valid_ratio_list';

export const agentParamStatsList = {
  [mountingFeatureType]: {
    [mountingInspection3D]: [
      'height_range',
      'rotation_offset',
      'x_offset',
      'y_offset',
      'max_slope',
    ],
    [mountingInspection2D]: [
      'threshold',
      'defect_check_threshold',
      'polarity_check_threshold',
    ],
  },
  [solderFeatureType]: {
    [solderInspection3D]: [
      'fillet_height_rate', // NOTE: this is not in reference param
      'fillet_volume_ratio', // NOTE: this is not in reference param
      'max_component_angle',
      'max_pcb_angle',
    ],
    [solderInspection2D]: [
      'valid_ratio_list' // NOTE: this is not in reference param
    ],
  },
  [leadGapFeatureType]: {
    [leadInspection2DBase]: [
      'bridge_threshold',
    ],
  },
  [leadFeatureType]: {
    [leadInspection2DBase]: [
      'lead_threshold',
    ],
    [leadInspection3D]: [
      'height_mean', // NOTE: this is not in reference param
      'height_std', // NOTE: this is not in reference param
    ],
    [leadInspection2D]: [
      'solder_valid_ratio_list', // NOTE: this is not in reference param
      'pad_valid_ratio_list', // NOTE: this is not in reference param
      'tip_valid_ratio_list', // NOTE: this is not in reference param
    ],
  },
};

export const focusRectStrokeWidth = 10;

export const defaultRoiColor = '#F4E76E';

export const newSubArrayBoardSelectionRoiOffset = 15; // 15 pixels

export const errorMessage = {
  startInferenceWithoutMarker: 'Alignment Marker not defined',
};

export const variantComponentErrorType = 'no_error(marked_as_variation)';